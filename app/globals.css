@import "tailwindcss";

:root {
  /* Healthcare Color Scheme */
  /* Primary Alert - Red/Crimson for urgency */
  --color-alert-primary: #dc2626; /* red-600 */
  --color-alert-primary-hover: #b91c1c; /* red-700 */
  --color-alert-primary-light: #fecaca; /* red-200 */
  
  /* Trust & Stability - Soft Blue */
  --color-trust: #3b82f6; /* blue-500 */
  --color-trust-hover: #2563eb; /* blue-600 */
  --color-trust-light: #dbeafe; /* blue-100 */
  --color-trust-dark: #1e40af; /* blue-800 */
  
  /* Success - Emerald Green */
  --color-success: #10b981; /* emerald-500 */
  --color-success-hover: #059669; /* emerald-600 */
  --color-success-light: #d1fae5; /* emerald-100 */
  
  /* Text - Charcoal/Navy */
  --color-text-primary: #1f2937; /* gray-800 */
  --color-text-secondary: #4b5563; /* gray-600 */
  --color-text-light: #6b7280; /* gray-500 */
  
  /* Warning - Amber/Gold */
  --color-warning: #f59e0b; /* amber-500 */
  --color-warning-hover: #d97706; /* amber-600 */
  --color-warning-light: #fef3c7; /* amber-100 */
  
  /* Background - Light Gray/White */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb; /* gray-50 */
  --color-bg-tertiary: #f3f4f6; /* gray-100 */
  
  /* Legacy variables for compatibility */
  --background: var(--color-bg-primary);
  --foreground: var(--color-text-primary);
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Dark mode adaptations */
    --color-bg-primary: #111827; /* gray-900 */
    --color-bg-secondary: #1f2937; /* gray-800 */
    --color-bg-tertiary: #374151; /* gray-700 */
    --color-text-primary: #f9fafb; /* gray-50 */
    --color-text-secondary: #d1d5db; /* gray-300 */
    --color-text-light: #9ca3af; /* gray-400 */
    
    --background: var(--color-bg-primary);
    --foreground: var(--color-text-primary);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Healthcare-specific utility classes */
.alert-primary {
  background-color: var(--color-alert-primary);
  color: white;
}

.alert-primary:hover {
  background-color: var(--color-alert-primary-hover);
}

.trust-primary {
  background-color: var(--color-trust);
  color: white;
}

.trust-primary:hover {
  background-color: var(--color-trust-hover);
}

.success-primary {
  background-color: var(--color-success);
  color: white;
}

.success-primary:hover {
  background-color: var(--color-success-hover);
}

.warning-primary {
  background-color: var(--color-warning);
  color: white;
}

.warning-primary:hover {
  background-color: var(--color-warning-hover);
}
